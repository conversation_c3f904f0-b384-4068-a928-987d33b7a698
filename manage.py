import os
import sys
import logging

from flask import url_for
from flask_admin import helpers as admin_helpers
from flask_script import Manager

from object_registry import finalize_app_initialization
from cataloging_service.app import create_app
from cataloging_service.extensions import admin
from cataloging_service.scripts.create_dynamic_price_property_skus import CreateDynamicPricePropertySkus

from cataloging_service.scripts.dedup_landmark import DedupLandmark
from cataloging_service.scripts.init_users import InitUserCommand
from cataloging_service.scripts.migrate_cost_center_ids import MigrateCostCenterIDs
from cataloging_service.scripts.migrate_global_policy import MigrateGlobalPolicyCommand
from cataloging_service.scripts.migrate_images import MigrateImages
from cataloging_service.scripts.migrate_landmark import MigrateLandmarkCommand
from cataloging_service.scripts.migrate_locality import MigrateLocalityCommand
from cataloging_service.scripts.migrate_location import MigrateLocationCommand
from cataloging_service.scripts.migrate_occupancy import MigrateOccupancyCommand
from cataloging_service.scripts.migrate_ota_mappings import MigrateOtaMappings
from cataloging_service.scripts.migrate_properties import Migrate<PERSON>roperty<PERSON>ommand
from cataloging_service.scripts.migrate_property_policy import Migrate<PERSON>ropertyPolicyCommand
from cataloging_service.scripts.migrate_property_policy_map import MigratePropertyPolicyMapCommand
from cataloging_service.scripts.migrate_reseller import MigrateResellerCommand
from cataloging_service.scripts.migrate_ruptub_legal_entities import (
    MigrateRuptubLegalEntitiesCommand,
)
from cataloging_service.scripts.push_sku_events import PushSkuEvents
from cataloging_service.scripts.set_amenity_summary import SetAmenitySummary
from cataloging_service.scripts.set_hotel_rate_plan import SetHotelRatePlan
from cataloging_service.scripts.set_min_room_size import SetMinRoomSize
from cataloging_service.scripts.set_room_config import SetRoomConfig
from cataloging_service.scripts.set_room_size import SetRoomSize
from cataloging_service.scripts.superhero_create_hotels import SuperheroCreateHotelsCommand
from cataloging_service.scripts.migrate_treebo_rates import MigrateTreeboPropertySkusRatesCommand

app = create_app()

manager = Manager(app)
manager.add_command("migrate_properties", MigratePropertyCommand)
manager.add_command("migrate_location", MigrateLocationCommand)
manager.add_command("init_users", InitUserCommand)
manager.add_command("set_room_config", SetRoomConfig)
manager.add_command("set_room_size", SetRoomSize)
manager.add_command("set_min_room_size", SetMinRoomSize)
manager.add_command("set_hotel_rate_plan", SetHotelRatePlan)
manager.add_command("set_amenity_summary", SetAmenitySummary)
manager.add_command("dedup_landmark", DedupLandmark)
manager.add_command("migrate_images", MigrateImages)
manager.add_command("migrate_ota_mappings", MigrateOtaMappings)
manager.add_command("migrate_landmark", MigrateLandmarkCommand)
manager.add_command("migrate_occupancy", MigrateOccupancyCommand)
manager.add_command("migrate_locality", MigrateLocalityCommand)
manager.add_command("migrate_reseller", MigrateResellerCommand)
manager.add_command("migrate_property_policy", MigratePropertyPolicyCommand)
manager.add_command("migrate_global_policy", MigrateGlobalPolicyCommand)
manager.add_command("migrate_property_policy_map", MigratePropertyPolicyMapCommand)
manager.add_command("migrate_ruptub_legal_entities", MigrateRuptubLegalEntitiesCommand)
manager.add_command("superhero_create_hotels", SuperheroCreateHotelsCommand)
manager.add_command('push_sku_events', PushSkuEvents)
manager.add_command("migrate_treebo_rates", MigrateTreeboPropertySkusRatesCommand)
manager.add_command("create_dynamic_price_property_skus", CreateDynamicPricePropertySkus)
manager.add_command("migrate_cost_center_ids", MigrateCostCenterIDs)

logger = logging.getLogger('error')


@app.context_processor
def security_context_processor():
    return dict(
        admin_base_template=admin.base_template,
        admin_view=admin.index_view,
        h=admin_helpers,
        get_url=url_for,
    )


if 'db' not in sys.argv:
    finalize_app_initialization()
